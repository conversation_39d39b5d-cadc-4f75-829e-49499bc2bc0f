<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!-- Merge after normal build -->
<!--    <Target Name="ILRepacker" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">-->
    <Target Name="ILRepacker" AfterTargets="Build">
        <ItemGroup>
            <InputAssemblies Include="$(OutputPath)$(AssemblyName).dll" />
            <InputAssemblies Include="$(OutputPath)MoonSharp.Interpreter.dll" />
        </ItemGroup>

        <ILRepack
                InputAssemblies="@(InputAssemblies)"
                OutputFile="$(OutputPath)$(AssemblyName).Merged.dll"
                TargetKind="Dll"
                Internalize="true"
                XmlDocumentation="true"
                DebugInfo="true"
                LibraryPath="$(OutputPath)"
                Parallel="true"
                Verbose="true" />

        <!-- Replace the build DLL with the merged one -->
        <Copy SourceFiles="$(OutputPath)$(AssemblyName).Merged.dll"
              DestinationFiles="$(OutputPath)$(AssemblyName).dll"
              OverwriteReadOnlyFiles="true" />
    </Target>

    <!-- Copy merged DLL and Scripts folder to Mods folder -->
    <Target Name="PostBuild" AfterTargets="ILRepacker" Inputs="@(Compile)" Outputs="$(MSBuildProjectFile).force">
        <!-- Copy the main DLL -->
        <Copy SourceFiles="$(OutputPath)$(AssemblyName).dll"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\"
              SkipUnchangedFiles="false" />

        <!-- Copy Scripts folder to TestLE subfolder in Mods -->
        <ItemGroup>
            <ScriptFiles Include="Scripts\**\*" />
        </ItemGroup>

        <Copy SourceFiles="@(ScriptFiles)"
              DestinationFolder="H:\SteamLibrary\steamapps\common\Last Epoch - Copy\Mods\TestLE\Scripts\%(RecursiveDir)"
              SkipUnchangedFiles="false" />
    </Target>

</Project>
