using System.Collections.Immutable;
using System.Reflection;
using System.Runtime.Loader;
using MelonLoader;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.Emit;

namespace TestLE.Scripting;

/// <summary>
/// Roslyn-based C# script compiler with hot reloading support.
/// Compiles C# scripts at runtime and manages assembly loading/unloading.
/// </summary>
public class CSharpCompiler
{
    private static CSharpCompiler? _instance;
    public static CSharpCompiler Instance => _instance ??= new CSharpCompiler();

    private readonly Dictionary<string, CompiledScript> _compiledScripts = new();
    private readonly List<MetadataReference> _references = new();
    private int _assemblyCounter = 0;

    public bool IsInitialized { get; private set; }
    public IReadOnlyDictionary<string, CompiledScript> CompiledScripts => _compiledScripts;

    private CSharpCompiler()
    {
        InitializeReferences();
    }

    public void Initialize()
    {
        if (IsInitialized) return;

        try
        {
            IsInitialized = true;
            MelonLogger.Msg("CSharpCompiler initialized successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize CSharpCompiler: {ex.Message}");
        }
    }

    public void Shutdown()
    {
        foreach (var script in _compiledScripts.Values)
        {
            script.Dispose();
        }
        _compiledScripts.Clear();
        IsInitialized = false;
    }

    /// <summary>
    /// Compile a C# script from source code.
    /// </summary>
    public CompilationResult CompileScript(string scriptName, string sourceCode)
    {
        try
        {
            // Dispose existing script if it exists
            if (_compiledScripts.TryGetValue(scriptName, out var existingScript))
            {
                existingScript.Dispose();
                _compiledScripts.Remove(scriptName);
            }

            var assemblyName = $"Script_{scriptName}_{++_assemblyCounter}";
            var syntaxTree = CSharpSyntaxTree.ParseText(sourceCode, path: $"{scriptName}.cs");

            var compilation = CSharpCompilation.Create(
                assemblyName,
                new[] { syntaxTree },
                _references,
                new CSharpCompilationOptions(
                    OutputKind.DynamicallyLinkedLibrary,
                    optimizationLevel: OptimizationLevel.Debug,
                    allowUnsafe: true
                )
            );

            using var ms = new MemoryStream();
            using var symbolsMs = new MemoryStream();

            var emitResult = compilation.Emit(ms, symbolsMs);

            if (!emitResult.Success)
            {
                var errors = emitResult.Diagnostics
                    .Where(d => d.Severity == DiagnosticSeverity.Error)
                    .Select(d => $"{d.Location.GetLineSpan().StartLinePosition}: {d.GetMessage()}")
                    .ToList();

                return CompilationResult.Failure(errors);
            }

            ms.Seek(0, SeekOrigin.Begin);
            symbolsMs.Seek(0, SeekOrigin.Begin);

            var context = new ScriptAssemblyLoadContext(assemblyName);
            var assembly = context.LoadFromStream(ms, symbolsMs);

            var compiledScript = new CompiledScript(scriptName, assembly, context, sourceCode);
            _compiledScripts[scriptName] = compiledScript;

            MelonLogger.Msg($"Successfully compiled script: {scriptName}");
            return CompilationResult.Success(compiledScript);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Compilation error for script '{scriptName}': {ex.Message}");
            return CompilationResult.Failure(new[] { ex.Message });
        }
    }

    /// <summary>
    /// Get a compiled script by name.
    /// </summary>
    public CompiledScript? GetCompiledScript(string scriptName)
    {
        return _compiledScripts.TryGetValue(scriptName, out var script) ? script : null;
    }

    /// <summary>
    /// Remove a compiled script and dispose its resources.
    /// </summary>
    public bool RemoveScript(string scriptName)
    {
        if (_compiledScripts.TryGetValue(scriptName, out var script))
        {
            script.Dispose();
            _compiledScripts.Remove(scriptName);
            return true;
        }
        return false;
    }

    private void InitializeReferences()
    {
        try
        {
            // Core .NET references
            _references.Add(MetadataReference.CreateFromFile(typeof(object).Assembly.Location));
            _references.Add(MetadataReference.CreateFromFile(typeof(Console).Assembly.Location));
            _references.Add(MetadataReference.CreateFromFile(typeof(IEnumerable<>).Assembly.Location));
            _references.Add(MetadataReference.CreateFromFile(typeof(System.Collections.IEnumerator).Assembly.Location));

            // Unity references
            var unityEngineAssembly = AppDomain.CurrentDomain.GetAssemblies()
                .FirstOrDefault(a => a.GetName().Name == "UnityEngine.CoreModule");
            if (unityEngineAssembly != null)
                _references.Add(MetadataReference.CreateFromFile(unityEngineAssembly.Location));

            // MelonLoader references
            _references.Add(MetadataReference.CreateFromFile(typeof(MelonLogger).Assembly.Location));

            // Current assembly (for accessing game types)
            _references.Add(MetadataReference.CreateFromFile(Assembly.GetExecutingAssembly().Location));

            // Il2Cpp references
            var il2cppAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => a.GetName().Name?.StartsWith("Il2Cpp") == true);
            foreach (var assembly in il2cppAssemblies)
            {
                try
                {
                    _references.Add(MetadataReference.CreateFromFile(assembly.Location));
                }
                catch
                {
                    // Skip assemblies that can't be referenced
                }
            }

            MelonLogger.Msg($"Initialized {_references.Count} compilation references");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize compilation references: {ex.Message}");
        }
    }
}

/// <summary>
/// Custom AssemblyLoadContext for script assemblies to enable unloading.
/// </summary>
public class ScriptAssemblyLoadContext : AssemblyLoadContext
{
    public ScriptAssemblyLoadContext(string name) : base(name, isCollectible: true)
    {
    }

    protected override Assembly? Load(AssemblyName assemblyName)
    {
        // Let the default context handle loading of system assemblies
        return null;
    }
}

/// <summary>
/// Represents a successfully compiled script.
/// </summary>
public class CompiledScript : IDisposable
{
    public string Name { get; }
    public Assembly Assembly { get; }
    public string SourceCode { get; }
    public ScriptAssemblyLoadContext LoadContext { get; }
    public DateTime CompilationTime { get; }

    private bool _disposed = false;

    public CompiledScript(string name, Assembly assembly, ScriptAssemblyLoadContext loadContext, string sourceCode)
    {
        Name = name;
        Assembly = assembly;
        LoadContext = loadContext;
        SourceCode = sourceCode;
        CompilationTime = DateTime.Now;
    }

    /// <summary>
    /// Create an instance of the script class.
    /// </summary>
    public T? CreateInstance<T>(string? typeName = null) where T : class
    {
        if (_disposed) return null;

        try
        {
            var type = typeName != null 
                ? Assembly.GetType(typeName) 
                : Assembly.GetTypes().FirstOrDefault(t => typeof(T).IsAssignableFrom(t));

            if (type == null) return null;

            return Activator.CreateInstance(type) as T;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to create instance of script '{Name}': {ex.Message}");
            return null;
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            LoadContext?.Unload();
            _disposed = true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error disposing script '{Name}': {ex.Message}");
        }
    }
}

/// <summary>
/// Result of script compilation.
/// </summary>
public class CompilationResult
{
    public bool Success { get; }
    public CompiledScript? CompiledScript { get; }
    public IReadOnlyList<string> Errors { get; }

    private CompilationResult(bool success, CompiledScript? compiledScript, IReadOnlyList<string> errors)
    {
        Success = success;
        CompiledScript = compiledScript;
        Errors = errors;
    }

    public static CompilationResult Success(CompiledScript compiledScript)
        => new(true, compiledScript, Array.Empty<string>());

    public static CompilationResult Failure(IEnumerable<string> errors)
        => new(false, null, errors.ToList());
}
