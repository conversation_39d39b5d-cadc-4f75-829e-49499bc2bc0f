using MelonLoader;
using MelonLoader.Utils;
using MoonSharp.Interpreter;

namespace TestLE.Scripting;

/// <summary>
/// Simple Lua script manager with hot reloading.
/// </summary>
public class LuaManager
{
    private static LuaManager? _instance;
    public static LuaManager Instance => _instance ??= new LuaManager();

    private readonly Dictionary<string, Script> _scripts = new();
    private readonly string _scriptsDirectory;
    private FileSystemWatcher? _fileWatcher;
    private readonly HashSet<string> _runningScripts = new();

    public bool IsInitialized { get; private set; }
    public IReadOnlyDictionary<string, Script> Scripts => _scripts;
    public IReadOnlySet<string> RunningScripts => _runningScripts;

    /// <summary>
    /// Get list of available script names (without .lua extension)
    /// </summary>
    public List<string> GetAvailableScripts()
    {
        if (!Directory.Exists(_scriptsDirectory))
            return new List<string>();

        return Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly)
            .Select(Path.GetFileNameWithoutExtension)
            .Where(name => !string.IsNullOrEmpty(name))
            .Cast<string>()
            .ToList();
    }

    private LuaManager()
    {
        _scriptsDirectory = Path.Combine(MelonEnvironment.ModsDirectory, "TestLE", "Scripts");
    }

    public void Initialize()
    {
        if (IsInitialized) return;

        try
        {
            Directory.CreateDirectory(_scriptsDirectory);

            // Register types with MoonSharp first
            LuaAPI.RegisterTypes();

            SetupFileWatcher();
            LoadAllScripts();

            IsInitialized = true;
            MelonLogger.Msg($"LuaManager initialized with {_scripts.Count} scripts");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize LuaManager: {ex.Message}");
        }
    }

    public void Shutdown()
    {
        _fileWatcher?.Dispose();
        _runningScripts.Clear();
        _scripts.Clear();
        IsInitialized = false;
    }

    public bool LoadScript(string scriptName)
    {
        try
        {
            var scriptPath = Path.Combine(_scriptsDirectory, $"{scriptName}.lua");
            if (!File.Exists(scriptPath)) return false;

            // Create script with proper CoreModules - work WITH MoonSharp
            var script = new Script(CoreModules.Preset_SoftSandbox);

            // Register our API
            LuaAPI.RegisterAPI(script);

            // Load and execute the script
            script.DoFile(scriptPath);

            _scripts[scriptName] = script;
            MelonLogger.Msg($"Loaded script: {scriptName}");
            return true;
        }
        catch (SyntaxErrorException ex)
        {
            MelonLogger.Error($"Lua syntax error in '{scriptName}': {ex.DecoratedMessage}");
            return false;
        }
        catch (ScriptRuntimeException ex)
        {
            MelonLogger.Error($"Lua runtime error in '{scriptName}': {ex.DecoratedMessage}");
            return false;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to load script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    public DynValue? CallFunction(string scriptName, string functionName, params object[] args)
    {
        if (!_scripts.TryGetValue(scriptName, out var script))
            return null;

        try
        {
            // Work WITH MoonSharp - use the proper Call method
            var function = script.Globals.Get(functionName);
            if (function.IsNil() || function.Type != DataType.Function)
                return null;

            return script.Call(function, args);
        }
        catch (ScriptRuntimeException ex)
        {
            MelonLogger.Error($"Lua runtime error in {scriptName}.{functionName}: {ex.DecoratedMessage}");
            return null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error calling {scriptName}.{functionName}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Start running a script. The script will have its OnUpdate function called each frame.
    /// </summary>
    public bool StartScript(string scriptName)
    {
        if (string.IsNullOrEmpty(scriptName))
            return false;

        // Load the script if not already loaded
        if (!_scripts.ContainsKey(scriptName))
        {
            if (!LoadScript(scriptName))
            {
                MelonLogger.Error($"Failed to load script: {scriptName}");
                return false;
            }
        }

        if (!_runningScripts.Add(scriptName))
            return false; // Already running
        
        MelonLogger.Msg($"Started Lua script: {scriptName}");
        return true;

    }

    /// <summary>
    /// Stop running a script.
    /// </summary>
    public bool StopScript(string scriptName)
    {
        if (!_runningScripts.Remove(scriptName))
            return false; // Wasn't running
        
        MelonLogger.Msg($"Stopped Lua script: {scriptName}");
        return true;

    }
    
    /// <summary>
    /// Stop all running scripts.
    /// </summary>
    public void StopAllScripts()
    {
        foreach (var script in _runningScripts.ToList())
        {
            StopScript(script);
            MelonLogger.Msg($"Stopped Lua script: {script}");
        }
    }

    /// <summary>
    /// Check if a script is currently running.
    /// </summary>
    public bool IsScriptRunning(string scriptName)
    {
        return _runningScripts.Contains(scriptName);
    }

    /// <summary>
    /// Update all running scripts by calling their OnUpdate function.
    /// Call this from your main OnUpdate method.
    /// </summary>
    public void UpdateRunningScripts()
    {
        foreach (var scriptName in _runningScripts.ToList()) // ToList to avoid modification during iteration
        {
            try
            {
                CallFunction(scriptName, "OnUpdate");
            }
            catch (Exception ex)
            {
                MelonLogger.Error($"Error updating script {scriptName}: {ex.Message}");
                _runningScripts.Remove(scriptName); // Stop the problematic script
            }
        }
    }

    private void LoadAllScripts()
    {
        var luaFiles = Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly);
        foreach (var filePath in luaFiles)
        {
            var scriptName = Path.GetFileNameWithoutExtension(filePath);
            if (!string.IsNullOrEmpty(scriptName))
                LoadScript(scriptName);
        }
    }

    private void SetupFileWatcher()
    {
        try
        {
            _fileWatcher = new FileSystemWatcher(_scriptsDirectory, "*.lua")
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName,
                EnableRaisingEvents = true
            };

            _fileWatcher.Changed += (_, e) => ReloadScript(e);
            _fileWatcher.Created += (_, e) => ReloadScript(e);
            _fileWatcher.Deleted += (_, e) => {
                var name = Path.GetFileNameWithoutExtension(e.Name);
                if (!string.IsNullOrEmpty(name)) _scripts.Remove(name);
            };
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Could not set up file watcher: {ex.Message}");
        }
    }

    private void ReloadScript(FileSystemEventArgs e)
    {
        var scriptName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(scriptName) && File.Exists(e.FullPath))
        {
            Task.Delay(100).ContinueWith(_ => {
                MelonLogger.Msg($"Reloading script: {scriptName}");
                LoadScript(scriptName);
            });
        }
    }
}
