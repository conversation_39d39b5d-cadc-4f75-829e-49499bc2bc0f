using System.Collections;
using Il2Cpp;
using MelonLoader;
using TestLE.Types;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// Base class for C# script routines that provides access to game APIs and common functionality.
/// Scripts should inherit from this class to get access to game objects and utility methods.
/// </summary>
public abstract class ScriptBase : IScriptRoutine
{
    /// <summary>
    /// Gets the name of this script routine.
    /// Override this to provide a custom name, or it will use the class name.
    /// </summary>
    public virtual string Name => GetType().Name;

    /// <summary>
    /// Determines whether this routine can execute in the current game state.
    /// Override this to implement custom execution conditions.
    /// </summary>
    public virtual bool CanExecute() => true;

    /// <summary>
    /// Executes the routine as a coroutine.
    /// Override this to implement the main script logic.
    /// </summary>
    public abstract IEnumerator Execute();

    /// <summary>
    /// Called when the script is first loaded or reloaded.
    /// Override this for initialization logic.
    /// </summary>
    public virtual void OnLoad()
    {
        Log($"Script {Name} loaded");
    }

    /// <summary>
    /// Called when the script is being unloaded (before hot reload).
    /// Override this for cleanup logic.
    /// </summary>
    public virtual void OnUnload()
    {
        Log($"Script {Name} unloaded");
    }

    #region Game API Access

    /// <summary>
    /// Gets the current player object.
    /// </summary>
    protected LocalPlayer? Player => PLAYER;

    /// <summary>
    /// Gets the list of current enemies.
    /// </summary>
    protected List<Enemy> Enemies => ENEMIES;

    /// <summary>
    /// Gets the list of current ground items.
    /// </summary>
    protected List<GroundItem> GroundItems => GROUND_ITEMS;

    /// <summary>
    /// Gets the list of current interactable objects.
    /// </summary>
    protected List<WorldObjectClickListener> Interactables => INTERACTABLES;

    /// <summary>
    /// Gets the current scene name.
    /// </summary>
    protected string CurrentScene => CURRENT_SCENE;

    #endregion

    #region Utility Methods

    /// <summary>
    /// Logs a message to the MelonLoader console.
    /// </summary>
    protected void Log(string message)
    {
        MelonLogger.Msg($"[{Name}] {message}");
    }

    /// <summary>
    /// Logs a warning message to the MelonLoader console.
    /// </summary>
    protected void LogWarning(string message)
    {
        MelonLogger.Warning($"[{Name}] {message}");
    }

    /// <summary>
    /// Logs an error message to the MelonLoader console.
    /// </summary>
    protected void LogError(string message)
    {
        MelonLogger.Error($"[{Name}] {message}");
    }

    /// <summary>
    /// Gets the current time in seconds since the game started.
    /// </summary>
    protected float Time => UnityEngine.Time.time;

    /// <summary>
    /// Gets the time since the last frame in seconds.
    /// </summary>
    protected float DeltaTime => UnityEngine.Time.deltaTime;

    /// <summary>
    /// Generates a random float between 0.0 and 1.0.
    /// </summary>
    protected float Random() => UnityEngine.Random.value;

    /// <summary>
    /// Generates a random float between min and max.
    /// </summary>
    protected float Random(float min, float max) => UnityEngine.Random.Range(min, max);

    /// <summary>
    /// Generates a random integer between min (inclusive) and max (exclusive).
    /// </summary>
    protected int Random(int min, int max) => UnityEngine.Random.Range(min, max);

    /// <summary>
    /// Checks if a key is currently being held down.
    /// </summary>
    protected bool GetKey(KeyCode key) => Input.GetKey(key);

    /// <summary>
    /// Checks if a key was pressed this frame.
    /// </summary>
    protected bool GetKeyDown(KeyCode key) => Input.GetKeyDown(key);

    /// <summary>
    /// Checks if a key was released this frame.
    /// </summary>
    protected bool GetKeyUp(KeyCode key) => Input.GetKeyUp(key);

    /// <summary>
    /// Waits for the specified number of seconds.
    /// </summary>
    protected IEnumerator Wait(float seconds)
    {
        yield return new WaitForSeconds(seconds);
    }

    /// <summary>
    /// Waits for one frame.
    /// </summary>
    protected IEnumerator WaitFrame()
    {
        yield return null;
    }

    /// <summary>
    /// Waits until a condition becomes true.
    /// </summary>
    protected IEnumerator WaitUntil(Func<bool> condition)
    {
        yield return new WaitUntil(condition);
    }

    /// <summary>
    /// Waits while a condition is true.
    /// </summary>
    protected IEnumerator WaitWhile(Func<bool> condition)
    {
        yield return new WaitWhile(condition);
    }

    #endregion

    #region Combat Utilities

    /// <summary>
    /// Gets the nearest enemy to the player.
    /// </summary>
    protected Enemy? GetNearestEnemy()
    {
        if (Player == null || Enemies.Count == 0) return null;

        var playerPos = Player.transform.position;
        return Enemies
            .Where(e => e != null && e.gameObject.activeInHierarchy)
            .OrderBy(e => Vector3.Distance(playerPos, e.transform.position))
            .FirstOrDefault();
    }

    /// <summary>
    /// Gets enemies within the specified distance from the player.
    /// </summary>
    protected List<Enemy> GetEnemiesInRange(float range)
    {
        if (Player == null) return new List<Enemy>();

        var playerPos = Player.transform.position;
        return Enemies
            .Where(e => e != null && e.gameObject.activeInHierarchy)
            .Where(e => Vector3.Distance(playerPos, e.transform.position) <= range)
            .ToList();
    }

    /// <summary>
    /// Gets the nearest ground item to the player.
    /// </summary>
    protected GroundItem? GetNearestGroundItem()
    {
        if (Player == null || GroundItems.Count == 0) return null;

        var playerPos = Player.transform.position;
        return GroundItems
            .Where(item => item != null && item.gameObject.activeInHierarchy)
            .OrderBy(item => Vector3.Distance(playerPos, item.transform.position))
            .FirstOrDefault();
    }

    /// <summary>
    /// Gets ground items within the specified distance from the player.
    /// </summary>
    protected List<GroundItem> GetGroundItemsInRange(float range)
    {
        if (Player == null) return new List<GroundItem>();

        var playerPos = Player.transform.position;
        return GroundItems
            .Where(item => item != null && item.gameObject.activeInHierarchy)
            .Where(item => Vector3.Distance(playerPos, item.transform.position) <= range)
            .ToList();
    }

    #endregion
}
