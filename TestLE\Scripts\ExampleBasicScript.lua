-- Simple Example Script
-- This demonstrates basic Lua integration

local lastTime = 0

function OnUpdate()
    local currentTime = Time()

    -- Only run every 5 seconds
    if currentTime - lastTime < 5.0 then
        return
    end

    lastTime = currentTime

    -- Get player info
    local player = GetPlayer()
    if player then
        local pos = GetPlayerPosition()
        if pos then
            Log("Player at: " .. pos.x .. ", " .. pos.y .. ", " .. pos.z)
        end

        local health = GetPlayerHealth()
        if health then
            Log("Player health: " .. health)
        end

        Log("Player alive: " .. tostring(IsPlayerAlive()))
        Log("Player in combat: " .. tostring(IsPlayerInCombat()))
    end

    -- Check for input
    if GetKeyDown("f1") then
        Log("F1 pressed!")
    end

    -- Random number
    Log("Random: " .. Random())
end
