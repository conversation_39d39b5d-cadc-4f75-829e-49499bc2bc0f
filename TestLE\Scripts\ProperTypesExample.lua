-- Proper MoonSharp Types Example
-- Demonstrates correct usage of registered types

local lastTime = 0
local counter = 0

function OnUpdate()
    local currentTime = Time()

    -- Only run every 3 seconds
    if currentTime - lastTime < 3.0 then
        return
    end

    lastTime = currentTime
    counter = counter + 1

    Log("=== Proper Types Example - Run " .. counter .. " ===")

    -- Test player access with proper UserData
    local player = GetPlayer()
    if not IsNil(player) then
        Log("Player found! Type: " .. Type(player))
        
        -- Get player position (returns UserData Vector3)
        local pos = GetPlayerPosition()
        if not IsNil(pos) then
            Log("Player position: " .. string.format("%.1f, %.1f, %.1f", pos.x, pos.y, pos.z))
            
            -- Create a new Vector3 and calculate distance
            local origin = Vector3(0, 0, 0)
            local distance = Distance(pos, origin)
            Log("Distance from origin: " .. string.format("%.2f", distance))
        else
            Log("Could not get player position")
        end

        -- Get player health (returns DynValue number)
        local health = GetPlayerHealth()
        if not <PERSON>N<PERSON>(health) then
            Log("Player health: " .. string.format("%.0f", health))
        else
            Log("Could not get player health")
        end

        -- Test boolean functions
        Log("Player alive: " .. tostring(IsPlayerAlive()))
    else
        Log("No player found")
    end

    -- Test collections with proper UserData
    local enemies = GetEnemies()
    if not IsNil(enemies) then
        Log("Enemies collection type: " .. Type(enemies))
        -- Note: Collection iteration would require additional setup
        -- This demonstrates that the collection is properly passed as UserData
    end

    local groundItems = GetGroundItems()
    if not IsNil(groundItems) then
        Log("Ground items collection type: " .. Type(groundItems))
    end

    -- Test math utilities
    local testVec1 = Vector3(1, 2, 3)
    local testVec2 = Vector3(4, 5, 6)
    local testDistance = Distance(testVec1, testVec2)
    Log("Test distance calculation: " .. string.format("%.2f", testDistance))

    -- Test math functions
    Log("Math test - Sqrt(16): " .. Sqrt(16))
    Log("Math test - Sin(1.57): " .. string.format("%.3f", Sin(1.57)))
    Log("Math test - Max(5, 10): " .. Max(5, 10))

    -- Test input
    if GetKeyDown("f1") then
        Log("F1 key pressed!")
    end

    -- Test time functions
    Log("Current time: " .. string.format("%.2f", Time()))
    Log("Delta time: " .. string.format("%.4f", DeltaTime()))
    Log("Frame count: " .. FrameCount())

    -- Test random
    Log("Random value: " .. string.format("%.3f", Random()))

    Log("=== End of run " .. counter .. " ===")
end

-- Example of a custom function that could be called from C#
function TestCustomFunction(message)
    Log("Custom function called with: " .. tostring(message))
    return "Response from Lua: " .. tostring(message)
end

-- Example of error handling
function SafeVectorOperation()
    local pos = GetPlayerPosition()
    if IsNil(pos) then
        Log("No position available for vector operation")
        return Vector3(0, 0, 0)
    end
    
    -- Safe vector manipulation
    local offset = Vector3(1, 0, 1)
    local newPos = Vector3(pos.x + offset.x, pos.y + offset.y, pos.z + offset.z)
    return newPos
end
