# TestLE Simple Lua Scripting

A simple Lua scripting system with hot reloading for TestLE mod.

## Features

- **Simple Lua Integration**: Basic MoonSharp integration
- **Hot Reload**: Scripts automatically reload when files change
- **Game API Access**: Access to player data, enemies, items, etc.
- **Easy Integration**: Just a few lines to add to your mod

## Getting Started

### Basic Script Structure

```lua
-- Simple script example
function OnUpdate()
    -- Your script logic here
    Log("Script running!")
end
```

### Available Functions

You can define these functions in your scripts:
- **OnUpdate()**: Called when you invoke the script
- Any custom function you want to call from C#

## Available API Functions

### Game Objects
```lua
local player = GetPlayer()
local enemies = GetEnemies()
local groundItems = GetGroundItems()
local interactables = GetInteractables()
```

### Player Functions
```lua
local position = GetPlayerPosition()
local health = GetPlayerHealth()
local isAlive = IsPlayerAlive()
local inCombat = IsPlayerInCombat()
```

### Utility Functions
```lua
Log("Message")                    -- Log to console
local distance = Distance(pos1, pos2)  -- Calculate distance
local vector = Vector3(x, y, z)   -- Create vector
local keyPressed = GetKey("f1")   -- Check key input
local keyDown = GetKeyDown("f1")  -- Check key press
local currentTime = Time()        -- Get current time
local randomValue = Random()      -- Get random 0-1
```

## Integration

Add to your main mod class:

```csharp
using TestLE.Scripting;

public override void OnApplicationStart()
{
    SimpleIntegration.Initialize();
}

public override void OnApplicationQuit()
{
    SimpleIntegration.Shutdown();
}

public override void OnUpdate()
{
    // Call script functions
    SimpleIntegration.CallScript("ExampleBasicScript", "OnUpdate");

    // Reload scripts with F9
    if (Input.GetKeyDown(KeyCode.F9))
        SimpleIntegration.ReloadScripts();
}
```

## Examples

See the example scripts:
- `ExampleBasicScript.lua` - Basic functionality
- `ExampleCombatScript.lua` - Combat monitoring
- `ExampleLootScript.lua` - Loot detection

## Hot Reload

Scripts automatically reload when you save changes to `.lua` files. No need to restart the game!
